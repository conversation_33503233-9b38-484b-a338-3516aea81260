name: Docker Build and Push

on:
  push:
    branches:
      - main
      - classical
      - dev
    tags:
      - "v*.*.*"
      - "v*"
      - "*.*.*"
      - "*.*.*-*"
  workflow_dispatch: # 允许手动触发工作流

# Workflow's jobs
jobs:
  build-amd64:
    name: Build AMD64 Image
    runs-on: ubuntu-24.04
    outputs:
      digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Check out git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Clone required dependencies
      - name: Clone maim_message
        run: git clone https://github.com/MaiM-with-u/maim_message maim_message

      - name: Clone lpmm
        run: git clone https://github.com/MaiM-with-u/MaiMBot-LPMM.git MaiMBot-LPMM

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      # Log in docker hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Generate metadata for Docker images
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKERHUB_USERNAME }}/maibot

      # Build and push AMD64 image by digest
      - name: Build and push AMD64
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          labels: ${{ steps.meta.outputs.labels }}
          file: ./Dockerfile
          cache-from: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/maibot:amd64-buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/maibot:amd64-buildcache,mode=max
          outputs: type=image,name=${{ secrets.DOCKERHUB_USERNAME }}/maibot,push-by-digest=true,name-canonical=true,push=true
          build-args: |
            BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
            VCS_REF=${{ github.sha }}

  build-arm64:
    name: Build ARM64 Image
    runs-on: ubuntu-24.04-arm
    outputs:
      digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Check out git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Clone required dependencies
      - name: Clone maim_message
        run: git clone https://github.com/MaiM-with-u/maim_message maim_message

      - name: Clone lpmm
        run: git clone https://github.com/MaiM-with-u/MaiMBot-LPMM.git MaiMBot-LPMM

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      # Log in docker hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Generate metadata for Docker images
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKERHUB_USERNAME }}/maibot

      # Build and push ARM64 image by digest
      - name: Build and push ARM64
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/arm64/v8
          labels: ${{ steps.meta.outputs.labels }}
          file: ./Dockerfile
          cache-from: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/maibot:arm64-buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/maibot:arm64-buildcache,mode=max
          outputs: type=image,name=${{ secrets.DOCKERHUB_USERNAME }}/maibot,push-by-digest=true,name-canonical=true,push=true
          build-args: |
            BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
            VCS_REF=${{ github.sha }}

  create-manifest:
    name: Create Multi-Arch Manifest
    runs-on: ubuntu-24.04
    needs:
      - build-amd64
      - build-arm64
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Log in docker hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Generate metadata for Docker images
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKERHUB_USERNAME }}/maibot
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha,prefix=${{ github.ref_name }}-,enable=${{ github.ref_type == 'branch' }}

      - name: Create and Push Manifest
        run: |
          # 为每个标签创建多架构镜像
          for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr '\n' ' '); do
            echo "Creating manifest for $tag"
            docker buildx imagetools create -t $tag \
              ${{ secrets.DOCKERHUB_USERNAME }}/maibot@${{ needs.build-amd64.outputs.digest }} \
              ${{ secrets.DOCKERHUB_USERNAME }}/maibot@${{ needs.build-arm64.outputs.digest }}
          done