# .github/workflows/precheck.yml
name: PR Precheck
on: [pull_request]

jobs:
  conflict-check:
    runs-on: [self-hosted, Windows, X64]
    outputs:
      conflict: ${{ steps.check-conflicts.outputs.conflict }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Check Conflicts
        id: check-conflicts
        run: |
          git fetch origin main
          $conflicts = git diff --name-only --diff-filter=U origin/main...HEAD
          if ($conflicts) {
            echo "conflict=true" >> $env:GITHUB_OUTPUT
            Write-Host "Conflicts detected in files: $conflicts"
          } else {
            echo "conflict=false" >> $env:GITHUB_OUTPUT
            Write-Host "No conflicts detected"
          }
        shell: pwsh
  labeler:
    runs-on: [self-hosted, Windows, X64]
    needs: conflict-check
    if: needs.conflict-check.outputs.conflict == 'true'
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ['🚫冲突需处理']
            })
