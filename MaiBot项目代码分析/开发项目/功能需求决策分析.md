# Enhanced Search Plugin 功能需求决策分析

## 🎯 项目概述

### 整体策略
将之前设计的3个独立插件（智能搜索、内容处理、多媒体工具）整合为一个**统一的增强搜索插件**，专注于**加强联网搜索机制**。

### 插件名称
`enhanced_search_plugin` - 增强搜索插件

### 核心目标
- **本地LLM优先** - 默认使用Ollama，零API成本
- **MCP工具增强** - 集成MCP工具加强联网搜索
- **功能完整保留** - 100%保留现有doubao_search_plugin功能
- **性能优化** - 适配2核8GB低功耗设备

## 📋 功能需求决策矩阵

### ✅ **要开发的核心功能**

#### **1. 联网搜索增强 (P0 - 最高优先级)**

##### **1.1 MCP工具集成**
- **DuckDuckGo MCP工具** - 免费搜索引擎
- **Bing CN MCP工具** - 中文搜索优化
- **Open Web Search MCP工具** - 开放网络搜索
- **Fetch MCP工具** - 网页内容获取
- **Trends Hub MCP工具** - 热点趋势搜索
- **Bilibili MCP工具** - B站内容搜索
- **Pulse CN MCP工具** - 中文资讯搜索
- **Web Browser MCP工具** - 浏览器自动化
- **12306 MCP工具** - 火车票查询

**决策理由**: MCP工具是加强联网搜索的核心，提供多样化搜索源

##### **1.2 搜索引擎保留**
- **Bing搜索** - 迁移现有功能
- **DuckDuckGo搜索** - 迁移现有功能
- **搜索结果聚合** - 多源结果整合
- **智能搜索调度** - 根据查询类型选择最佳搜索源

**决策理由**: 保持现有功能完整性，同时增强搜索能力

##### **1.3 本地LLM集成**
- **Ollama客户端** - 本地LLM服务
- **查询意图分析** - LLM分析用户查询意图
- **搜索策略制定** - 智能选择搜索工具组合
- **结果优化处理** - LLM优化搜索结果

**决策理由**: 核心功能，实现零API成本的智能搜索

#### **2. 内容处理功能 (P1 - 高优先级)**

##### **2.1 网页内容提取**
- **HTML内容提取** - 智能提取网页主要内容
- **内容清理过滤** - 移除广告、导航等无用内容
- **结构化数据提取** - 提取标题、正文、链接等

**决策理由**: 提升搜索结果质量，为用户提供清洁的内容

##### **2.2 内容质量评估**
- **质量评分系统** - 评估内容质量和相关性
- **去重机制** - 避免重复内容
- **相关性排序** - 按相关性排序结果

**决策理由**: 确保搜索结果的高质量和相关性

#### **3. 图片功能保留 (P1 - 高优先级)**

##### **3.1 现有图片功能**
- **Pixiv图片** - 迁移现有功能
- **Moehu图片** - 迁移现有功能
- **图片缓存** - 优化图片加载速度

**决策理由**: 用户常用功能，必须保留

#### **4. API兼容性 (P1 - 高优先级)**

##### **4.1 API接口保留**
- **豆包API** - 保留现有API，作为备选
- **自定义API接口** - 支持用户自定义LLM API
- **智能Fallback** - API不可用时自动切换

**决策理由**: 保持向后兼容，提供多种选择

#### **5. 网页截图功能 (P2 - 中优先级)**

##### **5.1 智能截图**
- **网页截图** - 完整网页截图功能
- **元素截图** - 特定元素截图
- **截图优化** - 图片质量和大小优化

**决策理由**: 增强搜索结果展示，提供可视化内容

#### **6. 文件下载管理 (P2 - 中优先级)**

##### **6.1 智能下载**
- **文件下载** - 支持各种文件类型下载
- **断点续传** - 支持下载中断后继续
- **批量下载** - 支持多文件并行下载
- **下载管理** - 下载进度监控和管理

**决策理由**: 增强搜索功能，支持文件资源获取

#### **7. QQ小程序生成 (P3 - 低优先级)**

##### **7.1 富媒体卡片**
- **搜索结果卡片** - 生成搜索结果展示卡片
- **图片画廊卡片** - 生成图片集合展示
- **新闻卡片** - 生成新闻内容卡片
- **模板管理** - 管理各种卡片模板

**决策理由**: 提升用户体验，提供丰富的内容展示

### ❌ **不开发的功能**

#### **1. 复杂文档处理**
- **PDF文档提取** - 复杂度高，使用频率低
- **Word文档处理** - 超出搜索插件范围
- **OCR图片识别** - 资源消耗大，低功耗设备不适合

**决策理由**: 专注网页搜索，避免资源浪费

#### **2. 高级文档处理**
- **PDF文档提取** - 复杂度高，使用频率低
- **Word文档处理** - 超出搜索插件范围
- **OCR图片识别** - 资源消耗大，低功耗设备不适合

**决策理由**: 专注网页搜索，避免资源浪费

#### **3. 独立知识库系统**
- **本地知识库** - MaiBot已有完整知识库
- **向量数据库** - 重复开发，增加复杂度
- **知识图谱** - 超出插件范围

**决策理由**: 充分利用MaiBot现有能力

#### **4. 复杂的AI决策系统**
- **独立决策引擎** - 与MaiBot主程序重复
- **复杂推理系统** - 超出插件职责
- **自主学习机制** - 过度设计

**决策理由**: 使用MaiBot现有的LLM机制

## 🏗️ 最终插件架构

### 目录结构
```
enhanced_search_plugin/
├── plugin.py                          # 主插件文件
├── _manifest.json                     # 插件清单
├── requirements.txt                   # 依赖文件
├── config/
│   ├── config.toml                    # 插件配置
│   └── mcp_tools_config.yaml          # MCP工具配置
├── actions/
│   ├── __init__.py
│   ├── enhanced_search_action.py      # 增强搜索Action
│   ├── image_fetch_action.py          # 图片获取Action
│   └── content_extract_action.py      # 内容提取Action
├── search/
│   ├── __init__.py
│   ├── search_dispatcher.py           # 搜索调度器
│   ├── mcp_manager.py                 # MCP工具管理器
│   ├── bing_search.py                 # Bing搜索引擎
│   ├── duckduckgo_search.py           # DuckDuckGo搜索引擎
│   └── result_aggregator.py           # 结果聚合器
├── mcp_tools/
│   ├── __init__.py
│   ├── duckduckgo_tool.py             # DuckDuckGo MCP工具
│   ├── bing_cn_tool.py                # Bing CN MCP工具
│   ├── open_web_tool.py               # Open Web MCP工具
│   ├── fetch_tool.py                  # Fetch MCP工具
│   ├── trends_hub_tool.py             # Trends Hub MCP工具
│   ├── bilibili_tool.py               # Bilibili MCP工具
│   ├── pulse_cn_tool.py               # Pulse CN MCP工具
│   ├── web_browser_tool.py            # Web Browser MCP工具
│   └── train_12306_tool.py            # 12306 MCP工具
├── content/
│   ├── __init__.py
│   ├── html_extractor.py              # HTML内容提取器
│   ├── content_cleaner.py             # 内容清理器
│   ├── quality_assessor.py            # 质量评估器
│   └── deduplicator.py                # 去重器
├── image/
│   ├── __init__.py
│   ├── pixiv_handler.py               # Pixiv图片处理
│   └── moehu_handler.py               # Moehu图片处理
├── screenshot/
│   ├── __init__.py
│   ├── screenshot_tool.py             # 网页截图工具
│   ├── element_capture.py             # 元素截图
│   └── image_optimizer.py             # 截图优化
├── download/
│   ├── __init__.py
│   ├── download_manager.py            # 下载管理器
│   ├── file_handler.py                # 文件处理器
│   └── progress_monitor.py            # 进度监控
├── miniapp/
│   ├── __init__.py
│   ├── card_generator.py              # 卡片生成器
│   ├── template_manager.py            # 模板管理器
│   └── media_formatter.py             # 媒体格式化器
├── llm/
│   ├── __init__.py
│   ├── ollama_client.py               # Ollama本地LLM客户端
│   ├── doubao_client.py               # 豆包API客户端
│   ├── custom_api_client.py           # 自定义API客户端
│   └── llm_manager.py                 # LLM管理器
└── utils/
    ├── __init__.py
    ├── cache_manager.py               # 缓存管理
    ├── config_manager.py              # 配置管理
    └── performance_monitor.py         # 性能监控
```

## 🎯 核心Action设计

### 1. EnhancedSearchAction
```
功能: 增强搜索 - MCP工具 + 传统搜索引擎 + 本地LLM
激活: 关键词 + LLM判断
流程: 查询分析 → 工具选择 → 并行搜索 → 结果聚合 → LLM优化
输出: 通过generator_api.rewrite_reply()处理
```

### 2. ImageFetchAction
```
功能: 图片获取 - Pixiv + Moehu
激活: 关键词激活
流程: 图片搜索 → 缓存检查 → 图片获取
输出: 直接发送图片
```

### 3. ContentExtractAction
```
功能: 内容提取 - 网页内容智能提取
激活: LLM判断
流程: URL解析 → 内容提取 → 质量评估 → 结构化输出
输出: 通过generator_api.rewrite_reply()处理
```

### 4. BrowserAction (集成截图+浏览功能)
```
功能: 内置浏览器 - 网页截图 + 嵌入式浏览 + 交互预览
激活: 关键词激活
流程: URL解析 → 浏览器启动 → 页面渲染 → 生成小型浏览器/截图
输出:
  - 网页截图图片
  - 嵌入式小型浏览器组件
  - 交互式网页预览
  - 网页内容摘要
```

### 5. DownloadAction
```
功能: 文件下载 - 智能文件下载和管理
激活: 关键词激活
流程: URL解析 → 文件类型检测 → 下载执行 → 进度监控 → 完成通知
输出: 下载状态和文件信息
```

### 6. MiniAppAction
```
功能: 小程序生成 - QQ小程序和富媒体卡片
激活: LLM判断
流程: 内容分析 → 模板选择 → 卡片生成 → 格式化输出
输出: 直接发送小程序卡片
```

## 📊 开发优先级

### P0 (最高优先级) - 第1周
1. **基础架构搭建** - 插件框架、配置系统
2. **Ollama集成** - 本地LLM客户端
3. **MCP工具集成** - 核心MCP工具包装

### P1 (高优先级) - 第2周
4. **搜索功能迁移** - Bing + DuckDuckGo迁移
5. **图片功能迁移** - Pixiv + Moehu迁移
6. **内容提取功能** - HTML提取和清理

### P2 (中优先级) - 第3周
7. **网页截图功能** - 智能截图和元素截图
8. **文件下载管理** - 文件下载和断点续传
9. **结果聚合优化** - 多源结果整合
10. **质量评估系统** - 内容质量评分

### P3 (低优先级) - 第4周
11. **QQ小程序生成** - 富媒体卡片和模板管理
12. **缓存和性能优化** - 提升响应速度
13. **高级配置** - 自定义API、高级设置
14. **监控和日志** - 性能监控和错误追踪
15. **测试和文档** - 完整测试和用户文档

## 🎯 成功标准

### 功能完整性
- ✅ 100%保留现有doubao_search_plugin功能
- ✅ 新增9个MCP工具增强搜索能力
- ✅ 新增网页截图、文件下载、小程序生成功能
- ✅ 本地LLM零成本运行

### 性能指标
- ✅ 搜索响应时间 < 15秒（包含MCP工具调用）
- ✅ 内存使用 < 600MB（包含MCP工具）
- ✅ 搜索成功率 > 95%

### 用户体验
- ✅ 无缝迁移，配置自动转换
- ✅ 搜索质量显著提升
- ✅ 稳定可靠，错误处理完善

---

**决策原则**: 专注搜索增强，避免功能膨胀
**核心理念**: MCP工具 + 本地LLM + 现有功能保留
**实现目标**: 零成本、高质量、强功能的搜索插件
