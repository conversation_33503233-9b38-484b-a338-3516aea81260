[inner]
version = "1.1.0"

#----以下是S4U聊天系统配置文件----
# S4U (Smart 4 U) 聊天系统是MaiBot的核心对话模块
# 支持优先级队列、消息中断、VIP用户等高级功能
#
# 如果你想要修改配置文件，请在修改后将version的值进行变更
# 如果新增项目，请参考src/mais4u/s4u_config.py中的S4UConfig类
#
# 版本格式：主版本号.次版本号.修订号
#----S4U配置说明结束----

[s4u]
# 消息管理配置
message_timeout_seconds = 80  # 普通消息存活时间（秒），超过此时间的消息将被丢弃
recent_message_keep_count = 8 # 保留最近N条消息，超出范围的普通消息将被移除

# 优先级系统配置
at_bot_priority_bonus = 100.0  # @机器人时的优先级加成分数
vip_queue_priority = true  # 是否启用VIP队列优先级系统
enable_message_interruption = true  # 是否允许高优先级消息中断当前回复

# 打字效果配置
typing_delay = 0.1  # 打字延迟时间（秒），模拟真实打字速度
enable_dynamic_typing_delay = false  # 是否启用基于文本长度的动态打字延迟

# 动态打字延迟参数（仅在enable_dynamic_typing_delay=true时生效）
chars_per_second = 15.0  # 每秒字符数，用于计算动态打字延迟
min_typing_delay = 0.2  # 最小打字延迟（秒）
max_typing_delay = 2.0  # 最大打字延迟（秒）

# 系统功能开关
enable_old_message_cleanup = true  # 是否自动清理过旧的普通消息
enable_loading_indicator = true  # 是否显示加载提示 

enable_streaming_output = false  # 是否启用流式输出，false时全部生成后一次性发送

max_context_message_length = 30
max_core_message_length = 20

# 模型配置
[models]
# 主要对话模型配置
[models.chat]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7
enable_thinking = false

# 规划模型配置
[models.motion]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7
enable_thinking = false

# 情感分析模型配置
[models.emotion]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 记忆模型配置
[models.memory]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 工具使用模型配置
[models.tool_use]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 嵌入模型配置
[models.embedding]
name = "text-embedding-v1"
provider = "OPENAI"
dimension = 1024

# 视觉语言模型配置
[models.vlm]
name = "qwen-vl-plus"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 知识库模型配置
[models.knowledge]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 实体提取模型配置
[models.entity_extract]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 问答模型配置
[models.qa]
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7

# 兼容性配置（已废弃，请使用models.motion）
[model_motion] # 在麦麦的一些组件中使用的小模型，消耗量较大，建议使用速度较快的小模型
# 强烈建议使用免费的小模型
name = "qwen3-8b"
provider = "BAILIAN"
pri_in = 0.5
pri_out = 2
temp = 0.7
enable_thinking = false # 是否启用思考