[inner]
version = "0.2.2"

# 配置文件版本号迭代规则同bot_config.toml
# 
# === 多API Key支持 ===
# 本配置文件支持为每个API服务商配置多个API Key，实现以下功能：
# 1. 错误自动切换：当某个API Key失败时，自动切换到下一个可用的Key
# 2. 负载均衡：在多个可用的API Key之间循环使用，避免单个Key的频率限制
# 3. 向后兼容：仍然支持单个key字段的配置方式
# 
# 配置方式：
# - 多Key配置：使用 api_keys = ["key1", "key2", "key3"] 数组格式
# - 单Key配置：使用 key = "your-key" 字符串格式（向后兼容）
# 
# 错误处理机制：
# - 401/403认证错误：立即切换到下一个API Key
# - 429频率限制：等待后重试，如果持续失败则切换Key
# - 网络错误：短暂等待后重试，失败则切换Key
# - 其他错误：按照正常重试机制处理
# 
# === 任务类型和模型能力配置 ===
# 为了提高任务分配的准确性和可维护性，现在支持明确配置模型的任务类型和能力：
# 
# task_type（推荐配置）:
# - 明确指定模型主要用于什么任务
# - 可选值：llm_normal, llm_reasoning, vision, embedding, speech
# - 如果不配置，系统会根据capabilities或模型名称自动推断（不推荐）
# 
# capabilities（推荐配置）:
# - 描述模型支持的所有能力
# - 可选值：text, vision, embedding, speech, tool_calling, reasoning
# - 支持多个能力的组合，如：["text", "vision"]
# 
# 配置优先级：
# 1. task_type（最高优先级，直接指定任务类型）
# 2. capabilities（中等优先级，根据能力推断任务类型）
# 3. 模型名称关键字（最低优先级，不推荐依赖）
# 
# 向后兼容：
# - 仍然支持 model_flags 字段，但建议迁移到 capabilities
# - 未配置新字段时会自动回退到基于模型名称的推断

[request_conf] # 请求配置（此配置项数值均为默认值，如想修改，请取消对应条目的注释）
#max_retry = 2 # 最大重试次数（单个模型API调用失败，最多重试的次数）
#timeout = 10 # API调用的超时时长（超过这个时长，本次请求将被视为“请求超时”，单位：秒）
#retry_interval = 10 # 重试间隔（如果API调用失败，重试的间隔时间，单位：秒）
#default_temperature = 0.7 # 默认的温度（如果bot_config.toml中没有设置temperature参数，默认使用这个值）
#default_max_tokens = 1024 # 默认的最大输出token数（如果bot_config.toml中没有设置max_tokens参数，默认使用这个值）


[[api_providers]] # API服务提供商（可以配置多个）
name = "DeepSeek"                       # API服务商名称（可随意命名，在models的api-provider中需使用这个命名）
base_url = "https://api.deepseek.cn/v1"    # API服务商的BaseURL
# 支持多个API Key，实现自动切换和负载均衡
api_keys = [                            # API Key列表（多个key支持错误自动切换和负载均衡）
    "sk-your-first-key-here",
    "sk-your-second-key-here",
    "sk-your-third-key-here"
]
# 向后兼容：如果只有一个key，也可以使用单个key字段
#api_key = "******"                         # API Key （可选，默认为None）
client_type = "openai"                  # 请求客户端（可选，默认值为"openai"，使用gimini等Google系模型时请配置为"gemini"）

[[api_providers]] # 特殊：Google的Gimini使用特殊API，与OpenAI格式不兼容，需要配置client为"gemini"
name = "Google"
base_url = "https://api.google.com/v1"
# Google API同样支持多key配置
api_keys = [
    "your-google-api-key-1",
    "your-google-api-key-2"
]
client_type = "gemini"

[[api_providers]]
name = "SiliconFlow"
base_url = "https://api.siliconflow.cn/v1"
# 单个key的示例（向后兼容）
api_key = "******"
#
#[[api_providers]]
#name = "LocalHost"
#base_url = "https://localhost:8888"
#key = "lm-studio"


[[models]] # 模型（可以配置多个）
# 模型标识符（API服务商提供的模型标识符）
model_identifier = "deepseek-chat"
# 模型名称（可随意命名，在bot_config.toml中需使用这个命名）
#（可选，若无该字段，则将自动使用model_identifier填充）
name = "deepseek-v3"
# API服务商名称（对应在api_providers中配置的服务商名称）
api_provider = "DeepSeek"
# 任务类型（推荐配置，明确指定模型主要用于什么任务）
# 可选值：llm_normal, llm_reasoning, vision, embedding, speech
# 如果不配置，系统会根据capabilities或模型名称自动推断
task_type = "llm_normal"
# 模型能力列表（推荐配置，描述模型支持的能力）
# 可选值：text, vision, embedding, speech, tool_calling, reasoning
capabilities = ["text", "tool_calling"]
# 输入价格（用于API调用统计，单位：元/兆token）（可选，若无该字段，默认值为0）
price_in = 2.0
# 输出价格（用于API调用统计，单位：元/兆token）（可选，若无该字段，默认值为0）
price_out = 8.0
# 强制流式输出模式（若模型不支持非流式输出，请取消该注释，启用强制流式输出）
#（可选，若无该字段，默认值为false）
#force_stream_mode = true

[[models]]
model_identifier = "deepseek-reasoner"
name = "deepseek-r1"
api_provider = "DeepSeek"
# 推理模型的配置示例
task_type = "llm_reasoning"
capabilities = ["text", "tool_calling", "reasoning"]
# 保留向后兼容的model_flags字段（已废弃，建议使用capabilities）
model_flags = [ "text", "tool_calling", "reasoning",]
price_in = 4.0
price_out = 16.0

[[models]]
model_identifier = "Pro/deepseek-ai/DeepSeek-V3"
name = "siliconflow-deepseek-v3"
api_provider = "SiliconFlow"
task_type = "llm_normal"
capabilities = ["text", "tool_calling"]
price_in = 2.0
price_out = 8.0

[[models]]
model_identifier = "Pro/deepseek-ai/DeepSeek-R1"
name = "siliconflow-deepseek-r1"
api_provider = "SiliconFlow"
task_type = "llm_reasoning"
capabilities = ["text", "tool_calling", "reasoning"]
price_in = 4.0
price_out = 16.0

[[models]]
model_identifier = "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B"
name = "deepseek-r1-distill-qwen-32b"
api_provider = "SiliconFlow"
task_type = "llm_reasoning"
capabilities = ["text", "tool_calling", "reasoning"]
price_in = 4.0
price_out = 16.0

[[models]]
model_identifier = "Qwen/Qwen3-8B"
name = "qwen3-8b"
api_provider = "SiliconFlow"
task_type = "llm_normal"
capabilities = ["text"]
price_in = 0
price_out = 0

[[models]]
model_identifier = "Qwen/Qwen3-14B"
name = "qwen3-14b"
api_provider = "SiliconFlow"
task_type = "llm_normal"
capabilities = ["text", "tool_calling"]
price_in = 0.5
price_out = 2.0

[[models]]
model_identifier = "Qwen/Qwen3-30B-A3B"
name = "qwen3-30b"
api_provider = "SiliconFlow"
task_type = "llm_normal"
capabilities = ["text", "tool_calling"]
price_in = 0.7
price_out = 2.8

[[models]]
model_identifier = "Qwen/Qwen2.5-VL-72B-Instruct"
name = "qwen2.5-vl-72b"
api_provider = "SiliconFlow"
# 视觉模型的配置示例
task_type = "vision"
capabilities = ["vision", "text"]
# 保留向后兼容的model_flags字段（已废弃，建议使用capabilities）
model_flags = [ "vision", "text",]
price_in = 4.13
price_out = 4.13

[[models]]
model_identifier = "FunAudioLLM/SenseVoiceSmall"
name = "sensevoice-small"
api_provider = "SiliconFlow"
# 语音模型的配置示例
task_type = "speech"
capabilities = ["speech"]
# 保留向后兼容的model_flags字段（已废弃，建议使用capabilities）
model_flags = [ "audio",]
price_in = 0
price_out = 0

[[models]]
model_identifier = "BAAI/bge-m3"
name = "bge-m3"
api_provider = "SiliconFlow"
# 嵌入模型的配置示例
task_type = "embedding"
capabilities = ["text", "embedding"]
# 保留向后兼容的model_flags字段（已废弃，建议使用capabilities）
model_flags = [ "text", "embedding",]
price_in = 0
price_out = 0


[task_model_usage]
llm_reasoning = {model="deepseek-r1", temperature=0.8, max_tokens=1024, max_retry=0}
llm_normal = {model="deepseek-r1", max_tokens=1024, max_retry=0}
embedding = "siliconflow-bge-m3"
#schedule = [
#    "deepseek-v3",
#    "deepseek-r1",
#]