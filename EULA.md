# **MaiBot最终用户许可协议**  
**版本：V1.1**  
**更新日期：2025年7月10日**  
**生效日期：2025年3月18日**  
**适用的MaiBot版本号：所有版本**   

**2025© MaiBot项目团队**

---

## 一、一般条款

**1.1** MaiBot项目（包括MaiBot的源代码、可执行文件、文档，以及其它在本协议中所列出的文件）（以下简称“本项目”）是由开发者及贡献者（以下简称“项目团队”）共同维护，为用户提供自动回复功能的机器人代码项目。以下最终用户许可协议（EULA，以下简称“本协议”）是用户（以下简称“您”）与项目团队之间关于使用本项目所订立的合同条件。

**1.2** 在运行或使用本项目之前，您**必须阅读并同意本协议的所有条款**。未成年人或其它无/不完全民事行为能力责任人请**在监护人的陪同下**阅读并同意本协议。如果您不同意，则不得运行或使用本项目。在这种情况下，您应立即从您的设备上卸载或删除本项目及其所有副本。


## 二、许可授权

### 源代码许可
**2.1** 您**了解**本项目的源代码是基于GPLv3（GNU通用公共许可证第三版）开源协议发布的。您**可以自由使用、修改、分发**本项目的源代码，但**必须遵守**GPLv3许可证的要求。详细内容请参阅项目仓库中的LICENSE文件。

**2.2** 您**了解**本项目的源代码中可能包含第三方开源代码，这些代码的许可证可能与GPLv3许可证不同。您**同意**在使用这些代码时**遵守**相应的许可证要求。


### 输入输出内容授权

**2.3** 您**了解**本项目是使用您的配置信息、提交的指令（以下简称“输入内容”）和生成的内容（以下简称“输出内容”）构建请求发送到第三方API生成回复的机器人项目。

**2.4** 您**授权**本项目使用您的输入和输出内容按照项目的隐私政策用于以下行为：
   - 调用第三方API生成回复；
   - 调用第三方API用于构建本项目专用的存储于您部署或使用的数据库中的知识库和记忆库；
   - 收集并记录本项目专用的存储于您部署或使用的设备中的日志；

**2.4** 您**了解**本项目的源代码中包含第三方API的调用代码，这些API的使用可能受到第三方的服务条款和隐私政策的约束。在使用这些API时，您**必须遵守**相应的服务条款。

**2.5** 项目团队**不对**第三方API的服务质量、稳定性、准确性、安全性负责，亦**不对**第三方API的服务变更、终止、限制等行为负责。


### 插件系统授权和责任免责

**2.6** 您**了解**本项目包含插件系统功能，允许加载和使用由第三方开发者（非MaiBot核心开发组成员）开发的插件。这些第三方插件可能具有独立的许可证条款和使用协议。

**2.7** 您**了解并同意**：
   - 第三方插件的开发、维护、分发由其各自的开发者负责，**与MaiBot项目团队无关**；
   - 第三方插件的功能、质量、安全性、合规性**完全由插件开发者负责**；
   - MaiBot项目团队**仅提供**插件系统的技术框架，**不对**任何第三方插件的内容、行为或后果承担责任；
   - 您使用任何第三方插件的风险**完全由您自行承担**；

**2.8** 在使用第三方插件前，您**应当**：
   - 仔细阅读并遵守插件开发者提供的许可证条款和使用协议；
   - 自行评估插件的安全性、合规性和适用性；
   - 确保插件的使用符合您所在地区的法律法规要求；


## 三、用户行为

**3.1** 您**了解**本项目会将您的配置信息、输入指令和生成内容发送到第三方API，您**不应**在输入指令和生成内容中包含以下内容：
   - 涉及任何国家或地区秘密、商业秘密或其他可能会对国家或地区安全或者公共利益造成不利影响的数据；
   - 涉及个人隐私、个人信息或其他敏感信息的数据；
   - 任何侵犯他人合法权益的内容；
   - 任何违反国家或地区法律法规、政策规定的内容；

**3.2** 您**不应**将本项目用于以下用途：
  - 违反任何国家或地区法律法规、政策规定的行为；

**3.3** 您**应当**自行确保您被存储在本项目的知识库、记忆库和日志中的输入和输出内容的合法性与合规性以及存储行为的合法性与合规性。您需**自行承担**由此产生的任何法律责任。

**3.4** 对于第三方插件的使用，您**不应**：
   - 使用可能存在安全漏洞、恶意代码或违法内容的插件；
   - 通过插件进行任何违反法律法规的行为；
   - 将插件用于侵犯他人权益或危害系统安全的用途；

**3.5** 您**承诺**对使用第三方插件的行为及其后果承担**完全责任**，包括但不限于因插件缺陷、恶意行为或不当使用造成的任何损失或法律纠纷。



## 四、免责条款

**4.1** 本项目的输出内容依赖第三方API，**不受**项目团队控制，亦**不代表**项目团队的观点。

**4.2** 除本协议条目2.4提到的隐私政策之外，项目团队**不会**对您提供任何形式的担保，亦**不对**使用本项目的造成的任何后果负责。

**4.3** 关于第三方插件，项目团队**明确声明**：
   - 项目团队**不对**任何第三方插件的功能、安全性、稳定性、合规性或适用性提供任何形式的保证或担保；
   - 项目团队**不对**因使用第三方插件而产生的任何直接或间接损失、数据丢失、系统故障、安全漏洞、法律纠纷或其他后果承担责任；
   - 第三方插件的质量问题、技术支持、bug修复等事宜应**直接联系插件开发者**，与项目团队无关；
   - 项目团队**保留**在不另行通知的情况下，对插件系统功能进行修改、限制或移除的权利；

## 五、其他条款

**5.1** 项目团队有权**随时修改本协议的条款**，但**没有**义务通知您。修改后的协议将在本项目的新版本中生效，您应定期检查本协议的最新版本。

**5.2** 项目团队**保留**本协议的最终解释权。


## 附录：其他重要须知

### 一、过往版本使用条件追溯

**1.1** 对于本项目此前未配备 EULA 协议的版本，自本协议发布之日起，若用户希望继续使用本项目，应在本协议生效后的合理时间内，通过升级到最新版本并同意本协议全部条款。若在本版协议生效日（2025年3月18日）之后，用户仍使用此前无 EULA 协议的项目版本且未同意本协议，则用户无权继续使用，项目方有权采取措施阻止其使用行为，并保留追究相关法律责任的权利。


### 二、风险提示

**2.1 隐私安全风险** 

   - 本项目会将您的配置信息、输入指令和生成内容发送到第三方API，而这些API的服务质量、稳定性、准确性、安全性不受项目团队控制。
   - 本项目会收集您的输入和输出内容，用于构建本项目专用的知识库和记忆库，以提高回复的准确性和连贯性。

      **因此，为了保障您的隐私信息安全，请注意以下事项：**

   - 避免在涉及个人隐私、个人信息或其他敏感信息的环境中使用本项目；
   - 避免在不可信的环境中使用本项目；

**2.2 精神健康风险** 

本项目仅为工具型机器人，不具备情感交互能力。建议用户：
   - 避免过度依赖AI回复处理现实问题或情绪困扰；  
   - 如感到心理不适，请及时寻求专业心理咨询服务。 
   - 如遇心理困扰，请寻求专业帮助（全国心理援助热线：12355）。   

**2.3 第三方插件风险**

本项目的插件系统允许加载第三方开发的插件，这可能带来以下风险：
   - **安全风险**：第三方插件可能包含恶意代码、安全漏洞或未知的安全威胁；
   - **稳定性风险**：插件可能导致系统崩溃、性能下降或功能异常；
   - **隐私风险**：插件可能收集、传输或泄露您的个人信息和数据；
   - **合规风险**：插件的功能或行为可能违反相关法律法规或平台规则；
   - **兼容性风险**：插件可能与主程序或其他插件产生冲突；

      **因此，在使用第三方插件时，请务必：**

   - 仅从可信来源获取和安装插件；
   - 在安装前仔细了解插件的功能、权限和开发者信息；
   - 定期检查和更新已安装的插件；
   - 如发现插件异常行为，请立即停止使用并卸载；
   - 对插件的使用后果承担完全责任；   

### 三、其他
**3.1 争议解决**
   - 本协议适用中国法律，争议提交相关地区法院管辖；  
   - 若因GPLv3许可产生纠纷，以许可证官方解释为准。  
