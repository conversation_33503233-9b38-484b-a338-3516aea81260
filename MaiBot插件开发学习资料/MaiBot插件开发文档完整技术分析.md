# MaiBot插件开发文档完整技术分析

## 文档获取范围

**主页面**: https://docs.mai-mai.org/develop/plugin_develop/
**子页面**: 快速开始、Manifest系统、Action组件、Command组件、Tool组件、配置管理、依赖管理、12个API参考页面

## 核心技术架构

### 插件系统架构
- **分层组件化设计**: 插件系统采用分层架构，包含插件管理层、组件注册层、API接口层
- **组件类型**: Action、Command、Tool、EventHandler四大核心组件类型
- **注册机制**: 使用`@register_plugin`装饰器进行插件注册
- **生命周期管理**: 完整的插件加载、初始化、运行、卸载生命周期

### 基础类继承体系
```python
# 插件基类
class BasePlugin:
    plugin_name: str
    enable_plugin: bool
    dependencies: List[str]
    python_dependencies: List[str]
    config_file_name: str
    config_schema: dict

# 组件基类
class BaseAction(ABC)
class BaseCommand(ABC)
class BaseTool(ABC)
class BaseEventHandler(ABC)
```

## Action组件技术分析

### 两层决策机制
**第一层: 激活控制 (Activation Control)**
- `ActionActivationType.NEVER`: 永不激活
- `ActionActivationType.ALWAYS`: 永远激活
- `ActionActivationType.LLM_JUDGE`: LLM智能判断激活
- `ActionActivationType.RANDOM`: 随机概率激活
- `ActionActivationType.KEYWORD`: 关键词触发激活

**第二层: 使用决策 (Usage Decision)**
- 基于`action_require`字段的使用场景描述
- 基于`action_parameters`的参数可执行性
- 结合聊天上下文的智能决策

### Action核心属性
```python
class BaseAction:
    action_name: str                    # 动作唯一标识符
    action_description: str             # 动作描述
    activation_type: ActionActivationType # 激活类型
    mode_enable: ChatMode              # 聊天模式启用
    associated_types: List[str]        # 关联消息类型
    parallel_action: bool              # 并行执行支持
    action_parameters: Dict[str, str]  # 参数说明
    action_require: List[str]          # 使用场景描述
```

### Action内置属性和方法
**内置属性**:
```python
self.log_prefix: str          # 日志前缀
self.group_id: str            # 群组ID
self.user_id: str             # 用户ID
self.platform: str            # 平台类型
self.chat_stream: ChatStream  # 聊天流对象
self.action_message: dict     # 消息数据
self.action_data: dict        # Action执行数据
```

**内置方法**:
```python
async def send_text(content: str, reply_to: str = "") -> bool
async def send_emoji(emoji_base64: str) -> bool
async def send_image(image_base64: str) -> bool
async def send_custom(message_type: str, content: str) -> bool
def get_config(key: str, default=None)
```

## Command组件技术分析

### Command核心特性
- **确定性执行**: 匹配到命令立即执行，无随机性
- **正则匹配**: 通过`command_pattern`正则表达式精确匹配
- **参数提取**: 使用命名捕获组`(?P<param_name>pattern)`提取参数
- **拦截控制**: 返回值控制是否阻止消息继续处理

### Command基本结构
```python
class BaseCommand:
    command_name: str        # 命令名称
    command_description: str # 命令描述
    command_pattern: str     # 正则匹配模式
    
    async def execute(self) -> Tuple[bool, Optional[str], bool]:
        # 返回: (执行成功, 结果消息, 是否拦截)
        return True, "执行成功", False
```

### 参数提取机制
```python
# 正则表达式示例
command_pattern = r"/example (?P<param1>\w+) (?P<param2>\w+)"

# 参数获取
param1 = self.matched_groups.get("param1")
param2 = self.matched_groups.get("param2")
```

## Tool组件技术分析

### Tool设计理念
- **信息获取增强**: 扩展麦麦获取外部信息的能力
- **LLM集成**: 可被LLM根据需要智能调用
- **插件式架构**: 支持独立开发和注册

### Tool vs Action vs Command对比
| 特征 | Action | Command | Tool |
|------|--------|---------|------|
| 主要用途 | 扩展麦麦行为能力 | 响应用户指令 | 扩展麦麦信息获取 |
| 触发方式 | 麦麦智能决策 | 用户主动触发 | LLM根据需要调用 |
| 目标 | 让麦麦做更多事情 | 提供具体功能 | 让麦麦知道更多信息 |

### Tool基本结构
```python
class BaseTool:
    name: str                    # 工具唯一名称
    description: str             # 工具功能描述
    parameters: List[Tuple]      # 参数定义
    available_for_llm: bool      # 是否对LLM可用
    
    async def execute(self, function_args: Dict[str, Any]) -> Dict:
        return {
            "name": self.name,
            "content": "执行结果"
        }
```

### 参数定义格式
```python
parameters = [
    ("query", "string", "查询参数", True),      # 必填参数
    ("limit", "integer", "结果数量限制", False)  # 可选参数
]
```

## 配置管理系统

### 双文件架构
**Manifest文件 (_manifest.json)**: 静态元数据
- 插件身份信息 (名称、版本、描述)
- 开发者信息 (作者、许可证、仓库)
- 系统信息 (兼容性、组件列表、分类)

**配置文件 (config.toml)**: 运行时配置
- 启用状态 (enabled)
- 功能参数配置
- 用户可调整的行为设置

### 配置版本管理机制

#### 版本管理工作流程
1. **版本检查**: 比较当前版本与期望版本
2. **配置迁移**: 版本不匹配时自动执行迁移
3. **结构更新**: 根据最新Schema生成新配置结构
4. **值迁移**: 将旧配置值迁移到新结构
5. **版本更新**: 自动更新config_version字段

#### 版本检查行为
- **无版本信息**: 跳过版本检查，直接加载现有配置
- **有版本信息**: 比较版本并执行必要的迁移
- **版本匹配**: 直接加载配置
- **版本不匹配**: 执行自动迁移流程

#### 配置迁移策略
- **保留原值优先**: 优先保留用户的原有配置值
- **新增字段默认值**: 新增配置项使用Schema中定义的默认值
- **移除字段警告**: 被移除的配置项在日志中显示警告
- **不保留备份**: 迁移后直接覆盖原配置文件

### Manifest文件结构
```json
{
  "manifest_version": 1,
  "name": "插件显示名称",
  "version": "1.0.0",
  "description": "插件功能描述",
  "author": {
    "name": "作者名称"
  },
  "license": "MIT",
  "host_application": {
    "min_version": "1.0.0",
    "max_version": "4.0.0"
  },
  "plugin_info": {
    "is_built_in": false,
    "plugin_type": "general",
    "components": []
  }
}
```

### ConfigField配置字段定义
```python
@dataclass
class ConfigField:
    """配置字段定义"""
    type: type                          # 字段类型 (str, int, float, bool, list)
    default: Any                        # 默认值
    description: str                    # 字段描述 (生成为配置文件注释)
    example: Optional[str] = None       # 示例值 (可选)
    required: bool = False              # 是否必需 (可选)
    choices: Optional[List[Any]] = None # 可选值列表 (可选)
```

### 配置Schema定义
```python
config_schema = {
    "plugin": {
        "enabled": ConfigField(type=bool, default=True, description="是否启用插件"),
        "config_version": ConfigField(type=str, default="1.0.0", description="配置文件版本"),
        "max_retries": ConfigField(type=int, default=3, description="最大重试次数"),
    },
    "features": {
        "auto_reply": ConfigField(type=bool, default=False, description="自动回复"),
        "keywords": ConfigField(type=list, default=[], description="关键词列表"),
        "log_level": ConfigField(
            type=str,
            default="INFO",
            description="日志级别",
            choices=["DEBUG", "INFO", "WARNING", "ERROR"]
        ),
    }
}
```

### 配置节描述定义
```python
config_section_descriptions = {
    "plugin": "插件启用配置",
    "components": "组件启用控制",
    "features": "功能特性配置",
    "logging": "日志记录相关配置"
}
```

## 消息类型系统

### 支持的消息类型 (associated_types)
| 类型 | 说明 | 格式 |
|------|------|------|
| text | 文本消息 | str |
| emoji | 表情消息 | str: 表情包的无头base64 |
| image | 图片消息 | str: 图片的无头base64 |
| reply | 回复消息 | str: 回复的消息ID |
| voice | 语音消息 | str: wav格式语音的无头base64 |
| command | 命令消息 | 参见Adapter文档 |
| voiceurl | 语音URL消息 | str: wav格式语音的URL |
| music | 音乐消息 | str: 网易云音乐的音乐id |
| videourl | 视频URL消息 | str: 视频的URL |
| file | 文件消息 | str: 文件的路径 |

## 插件开发工作流程

### 1. 插件目录结构
```
plugins/my_plugin/
├── _manifest.json        # 插件清单文件
├── plugin.py            # 插件主文件
├── config.toml          # 插件配置文件 (自动生成)
└── README.md            # 插件说明文档
```

### 2. 插件注册流程
```python
@register_plugin
class MyPlugin(BasePlugin):
    plugin_name = "my_plugin"
    enable_plugin = True
    
    def get_plugin_components(self) -> List[Tuple[ComponentInfo, Type]]:
        return [
            (MyAction.get_action_info(), MyAction),
            (MyCommand.get_command_info(), MyCommand),
        ]
```

### 3. 组件实现模式
```python
# Action组件实现
class MyAction(BaseAction):
    action_name = "my_action"
    activation_type = ActionActivationType.KEYWORD
    activation_keywords = ["关键词"]
    
    async def execute(self) -> Tuple[bool, str]:
        await self.send_text("执行结果")
        return True, "执行成功"

# Command组件实现
class MyCommand(BaseCommand):
    command_name = "my_command"
    command_pattern = r"^/cmd (?P<param>\w+)$"
    
    async def execute(self) -> Tuple[bool, str, bool]:
        param = self.matched_groups.get("param")
        await self.send_text(f"参数: {param}")
        return True, "执行成功", True
```

## 依赖管理系统

### PythonDependency类
```python
PythonDependency(
    package_name="PIL",          # 导入时的包名
    version=">=11.2.0",          # 版本要求
    optional=False,              # 是否为可选依赖
    description="图像处理库",     # 依赖描述
    install_name="pillow"        # pip安装时的包名
)
```

### 版本格式支持
```python
PythonDependency("requests", ">=2.25.0")           # 最小版本
PythonDependency("numpy", ">=1.20.0,<2.0.0")       # 版本范围
PythonDependency("pillow", "==8.3.2")              # 精确版本
PythonDependency("scipy", ">=1.7.0,!=1.8.0")       # 排除特定版本
```

## 管理工具

### manifest_tool.py工具
```bash
# 扫描缺少manifest的插件
python scripts/manifest_tool.py scan src/plugins

# 创建最小化manifest文件
python scripts/manifest_tool.py create-minimal src/plugins/my_plugin --name "我的插件"

# 创建完整manifest模板
python scripts/manifest_tool.py create-complete src/plugins/my_plugin

# 验证manifest文件
python scripts/manifest_tool.py validate src/plugins/my_plugin
```

## 技术限制和约束

### 当前限制
1. **Manifest版本**: 当前只支持`manifest_version = 1`
2. **编码格式**: 所有文件必须使用UTF-8编码
3. **JSON格式**: Manifest文件必须是有效的JSON格式
4. **必需字段**: `manifest_version`、`name`、`version`、`description`、`author.name`为必需字段

### 开发约束
1. **插件命名**: 插件名称必须唯一，不能与现有插件冲突
2. **组件命名**: 同类型组件名称在全局范围内必须唯一
3. **配置文件**: 不要手动创建config.toml文件，应通过Schema自动生成
4. **依赖管理**: 使用PythonDependency类管理Python包依赖

## 核心设计模式

### 装饰器模式
- `@register_plugin`: 插件注册装饰器
- 自动发现和注册机制

### 工厂模式
- 组件实例化通过工厂方法
- `get_plugin_components()`返回组件信息和类型

### 策略模式
- Action的多种激活策略
- 不同的决策算法封装

### 观察者模式
- EventHandler组件实现事件监听
- 事件驱动的组件通信

这套技术架构体现了现代软件工程的最佳实践，通过分层设计、组件化架构、配置驱动等方式实现了高度的可扩展性和可维护性。
