# MaiBot插件API完整技术分析

## API系统架构

### 导入机制
```python
# 统一导入方式
from src.plugin_system.apis import send_api, llm_api, emoji_api
# 或者
from src.plugin_system import send_api, llm_api, emoji_api
```

### API模块分类
- **消息处理类**: send_api, message_api, chat_api
- **AI生成类**: llm_api, generator_api
- **数据管理类**: database_api, config_api, person_api
- **系统管理类**: plugin_manage_api, component_manage_api, logging_api
- **功能扩展类**: emoji_api, tool_api

## 1. 消息发送API (send_api)

### 核心函数签名
```python
# 发送文本消息
async def text_to_stream(
    text: str,
    stream_id: str,
    typing: bool = False,
    reply_to: str = "",
    storage_message: bool = True,
) -> bool

# 发送表情包
async def emoji_to_stream(
    emoji_base64: str, 
    stream_id: str, 
    storage_message: bool = True
) -> bool

# 发送图片
async def image_to_stream(
    image_base64: str, 
    stream_id: str, 
    storage_message: bool = True
) -> bool

# 发送命令
async def command_to_stream(
    command: Union[str, dict], 
    stream_id: str, 
    storage_message: bool = True, 
    display_message: str = ""
) -> bool

# 发送自定义类型消息
async def custom_to_stream(
    message_type: str,
    content: str,
    stream_id: str,
    display_message: str = "",
    typing: bool = False,
    reply_to: str = "",
    storage_message: bool = True,
    show_log: bool = True,
) -> bool
```

### 消息类型支持
| 类型 | 说明 | 内容格式 |
|------|------|----------|
| text | 纯文本消息 | str |
| emoji | 表情包消息 | base64编码字符串 |
| image | 图片消息 | base64编码字符串 |
| command | 命令消息 | str或dict |
| video | 视频消息 | base64编码或URL |
| audio | 音频消息 | base64编码或URL |

### 回复格式规范
- 格式: `"发送者:消息内容"` 或 `"发送者：消息内容"`
- 系统自动查找匹配的原始消息进行回复
- 支持中英文冒号分隔符

### 参数详解
- **typing**: 显示"正在输入"状态
- **reply_to**: 回复特定消息
- **storage_message**: 是否存储到数据库
- **display_message**: 显示给用户的消息内容
- **show_log**: 是否在日志中显示

## 2. 消息查询API (message_api)

### 核心查询函数
```python
# 按时间范围查询消息
def get_messages_by_time(
    start_time: float,
    end_time: float,
    limit: int = 50,
    filter_mai: bool = False
) -> List[MessageRecv]

# 查询指定聊天中的消息
def get_messages_by_time_in_chat(
    chat_stream: ChatStream,
    start_time: float,
    end_time: float,
    limit: int = 50,
    filter_mai: bool = False
) -> List[MessageRecv]

# 查询指定用户的消息
def get_messages_by_time_for_users(
    user_ids: List[str],
    start_time: float,
    end_time: float,
    limit: int = 50,
    filter_mai: bool = False
) -> List[MessageRecv]

# 获取最近消息
def get_recent_messages(
    limit: int = 50,
    filter_mai: bool = False
) -> List[MessageRecv]

# 统计新消息数量
def count_new_messages(since_time: float) -> int

# 格式化消息为可读字符串
def build_readable_messages_to_str(
    messages: List[MessageRecv],
    replace_bot_name: bool = True,
    merge_messages: bool = False
) -> str
```

### 消息过滤参数
- **filter_mai**: 是否过滤机器人自己发送的消息
- **limit**: 返回消息数量限制
- **start_time/end_time**: Unix时间戳范围

### 消息格式化选项
- **replace_bot_name**: 将机器人名称替换为"我"
- **merge_messages**: 合并连续的同一发送者消息

## 3. 聊天流API (chat_api)

### 聊天流管理函数
```python
# 获取所有聊天流
def get_all_streams() -> List[ChatStream]

# 获取群聊聊天流
def get_group_streams() -> List[ChatStream]

# 获取私聊聊天流
def get_private_streams() -> List[ChatStream]

# 根据群ID获取聊天流
def get_stream_by_group_id(group_id: str) -> Optional[ChatStream]

# 根据用户ID获取私聊流
def get_stream_by_user_id(user_id: str) -> Optional[ChatStream]

# 获取聊天流详细信息
def get_stream_info(stream_id: str) -> Optional[Dict[str, Any]]

# 获取聊天流统计摘要
def get_streams_summary() -> Dict[str, int]
```

### ChatStream对象属性
```python
class ChatStream:
    stream_id: str          # 聊天流唯一标识
    stream_type: str        # 聊天类型 (group/private)
    group_id: Optional[str] # 群组ID (群聊时)
    user_id: Optional[str]  # 用户ID (私聊时)
    platform: str           # 平台类型
    is_active: bool         # 是否活跃
    last_message_time: float # 最后消息时间
```

## 4. LLM API (llm_api)

### 核心函数签名
```python
# 获取可用模型列表
def get_available_models() -> Dict[str, TaskConfig]

# 使用指定模型生成内容
async def generate_with_model(
    prompt: str,
    model_config: TaskConfig,
    request_type: str = "plugin.generate",
    **kwargs
) -> Tuple[bool, str, str, str]
```

### 函数详解

#### get_available_models()
- **功能**: 获取所有可用的模型配置
- **返回值**: Dict[str, TaskConfig] - 模型配置字典，key为模型名称，value为模型配置对象

#### generate_with_model()
- **功能**: 使用指定模型生成内容
- **参数**:
  - `prompt`: 提示词
  - `model_config`: 模型配置对象（从get_available_models获取）
  - `request_type`: 请求类型标识，默认为"plugin.generate"
  - `**kwargs`: 其他模型特定参数，如temperature、max_tokens等
- **返回值**: Tuple[bool, str, str, str] - (是否成功, 生成的内容, 推理过程, 模型名称)

### TaskConfig对象结构
```python
# TaskConfig是模型配置对象，包含模型的所有配置信息
# 具体结构需要参考系统配置文件
model_config = get_available_models()["default"]  # 获取默认模型配置
```

### 使用示例
```python
# 获取可用模型
models = llm_api.get_available_models()
default_model = models.get("default")

if default_model:
    success, content, reasoning, model_name = await llm_api.generate_with_model(
        prompt="请介绍一下自己",
        model_config=default_model,
        request_type="plugin.introduction",
        temperature=0.7,
        max_tokens=500
    )

    if success:
        print(f"生成内容: {content}")
        print(f"使用模型: {model_name}")
        print(f"推理过程: {reasoning}")
```

## 5. 回复生成器API (generator_api)

### 回复生成函数
```python
# 获取回复器对象
def get_replyer() -> Any

# 生成智能回复
async def generate_reply(
    message: MessageRecv,
    context: Optional[str] = None
) -> List[Tuple[str, str]]

# 重写回复内容
async def rewrite_reply(
    original_reply: str,
    style: str = "friendly"
) -> str

# 自定义提示词回复
async def generate_response_custom(
    prompt: str,
    message: MessageRecv
) -> str
```

### 回复集合格式
```python
reply_set = [
    ("text", "很高兴见到你！"),
    ("emoji", "emoji_base64_data"),
    ("text", "有什么可以帮助你的吗？")
]
```

### 回复类型
- **text**: 文本回复
- **emoji**: 表情包回复
- **image**: 图片回复
- **command**: 命令回复

## 6. 表情包API (emoji_api)

### 核心函数签名
```python
# 根据场景描述选择表情包
async def get_by_description(description: str) -> Optional[Tuple[str, str, str]]

# 根据情感标签获取表情包
async def get_by_emotion(emotion: str) -> Optional[Tuple[str, str, str]]

# 随机获取表情包
async def get_random(count: Optional[int] = 1) -> List[Tuple[str, str, str]]

# 获取表情包数量
def get_count() -> int

# 获取表情包系统信息
def get_info() -> Dict[str, Any]

# 获取所有可用情感标签
def get_emotions() -> List[str]

# 获取所有表情包描述
def get_descriptions() -> List[str]
```

### 二步走识别优化机制

#### 收到表情包时的识别流程
1. **第一步**: VLM视觉分析 - 生成详细描述
2. **第二步**: LLM情感分析 - 基于详细描述提取核心情感标签
3. **缓存机制**: 将情感标签缓存到数据库，详细描述保存到Images表

#### 注册表情包时的优化
- **智能复用**: 优先从Images表获取已有的详细描述
- **避免重复**: 如果表情包之前被收到过，跳过VLM调用
- **性能提升**: 减少不必要的AI调用，降低延时和成本

#### 缓存策略
- **ImageDescriptions表**: 缓存最终的情感标签（用于快速显示）
- **Images表**: 保存详细描述（用于注册时复用）
- **双重检查**: 防止并发情况下的重复生成

### 返回值格式
```python
# 返回元组: (base64编码, 描述, 情感标签)
emoji_result = ("iVBORw0KGgoAAAANSUhEUgAA...", "开心的表情", "happy")
```

### 场景描述和匹配机制

#### 常用场景描述
- **开心类场景**: 开心的大笑、满意的微笑、兴奋的手舞足蹈
- **无奈类场景**: 表示无奈和沮丧、轻微的讽刺、无语的摇头
- **愤怒类场景**: 愤怒和不满、生气的瞪视、暴躁的抓狂
- **惊讶类场景**: 震惊的表情、意外的发现、困惑的思考
- **可爱类场景**: 卖萌的表情、撒娇的动作、害羞的样子

#### 匹配机制
- **精确匹配**: 优先匹配完整的场景描述，如"开心的大笑"
- **关键词匹配**: 如果没有精确匹配，则根据关键词进行模糊匹配
- **语义匹配**: 系统会理解场景的语义含义进行智能匹配

### 使用示例
```python
# 根据描述获取表情包
emoji_result = await emoji_api.get_by_description("大笑")
if emoji_result:
    emoji_base64, description, emotion = emoji_result
    await send_api.emoji_to_stream(emoji_base64, stream_id)

# 根据情感获取表情包
emoji_result = await emoji_api.get_by_emotion("happy")

# 随机获取多个表情包
emojis = await emoji_api.get_random(3)
for emoji_base64, description, emotion in emojis:
    print(f"表情包: {description}, 情感: {emotion}")

# 获取系统信息
info = emoji_api.get_info()
print(f"当前表情包数量: {info['current_count']}")
```

## 7. 人物信息API (person_api)

### 用户信息查询函数
```python
# 根据平台和用户ID获取person_id
def get_person_id(platform: str, user_id: str) -> Optional[int]

# 查询单个用户信息字段值
def get_person_value(person_id: int, field: str) -> Any

# 批量获取用户信息字段值
def get_person_values(person_id: int, fields: List[str]) -> Dict[str, Any]

# 判断用户是否已知
def is_person_known(platform: str, user_id: str) -> bool

# 根据用户名获取Person ID
def get_person_id_by_name(name: str) -> Optional[int]
```

### 常用字段
- **基础信息**: nickname, platform, user_id
- **关系信息**: impression, points, relationship_level
- **统计信息**: message_count, last_seen_time
- **个性化**: preferred_style, interests

### 用户关系系统
- **impression**: 用户印象分数
- **points**: 用户积分
- **relationship_level**: 关系等级
- **interaction_history**: 交互历史

## 8. 数据库API (database_api)

### 通用数据库操作
```python
async def db_query(
    model_class: Type[Model],
    data: Optional[Dict[str, Any]] = None,
    query_type: Optional[str] = "get",
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    order_by: Optional[List[str]] = None,
    single_result: Optional[bool] = False,
) -> Union[List[Dict[str, Any]], Dict[str, Any], None]
```

### 查询类型
- **get**: 查询数据
- **create**: 创建记录
- **update**: 更新记录
- **delete**: 删除记录
- **count**: 统计数量

### 过滤器格式
```python
filters = {
    "field_name": "value",           # 等于
    "field_name__gt": 100,           # 大于
    "field_name__lt": 200,           # 小于
    "field_name__in": [1, 2, 3],     # 包含
    "field_name__like": "%pattern%"   # 模糊匹配
}
```

### 排序格式
```python
order_by = [
    "field_name",      # 升序
    "-field_name",     # 降序
    "field1", "-field2" # 多字段排序
]
```

## API使用模式和最佳实践

### 异步编程模式
```python
# 所有API调用都应该使用异步模式
async def my_function():
    # 正确的异步调用
    result = await send_api.text_to_stream("Hello", stream_id)
    
    # 错误的同步调用
    # result = send_api.text_to_stream("Hello", stream_id)  # 这会出错
```

### 错误处理模式
```python
async def safe_api_call():
    try:
        success = await send_api.text_to_stream("Hello", stream_id)
        if not success:
            logger.warning("消息发送失败")
            return False
    except Exception as e:
        logger.error(f"API调用异常: {e}")
        return False
    return True
```

### 批量操作模式
```python
async def batch_send_messages(messages: List[str], stream_id: str):
    results = []
    for message in messages:
        result = await send_api.text_to_stream(message, stream_id)
        results.append(result)
        # 添加延迟避免频率限制
        await asyncio.sleep(0.1)
    return results
```

### 资源管理模式
```python
async def resource_managed_operation():
    # 获取资源
    messages = message_api.get_recent_messages(limit=100)
    
    try:
        # 处理资源
        for message in messages:
            await process_message(message)
    finally:
        # 清理资源（如果需要）
        del messages
```

## 9. 配置API (config_api)

### 核心函数签名
```python
# 访问全局配置
def get_global_config(key: str, default: Any = None) -> Any

# 获取插件配置
def get_plugin_config(plugin_config: dict, key: str, default: Any = None) -> Any
```

### 函数详解

#### get_global_config()
- **功能**: 访问全局配置信息
- **参数**:
  - `key`: 命名空间式配置键名，使用嵌套访问，如 "section.subsection.key"
  - `default`: 如果配置不存在时返回的默认值
- **返回值**: 配置值或默认值

#### get_plugin_config()
- **功能**: 获取插件特定的配置值
- **参数**:
  - `plugin_config`: 插件配置字典
  - `key`: 配置键名，支持嵌套访问
  - `default`: 默认值
- **返回值**: 配置值或默认值

### 使用示例
```python
# 获取机器人昵称
bot_name = config_api.get_global_config("bot.nickname", "MaiBot")

# 获取插件配置
plugin_setting = config_api.get_plugin_config(
    plugin_config,
    "feature.enabled",
    False
)
```

## 10. 插件管理API (plugin_manage_api)

### 核心函数签名
```python
# 插件查询
def list_loaded_plugins() -> List[str]
def list_registered_plugins() -> List[str]
def get_plugin_path(plugin_name: str) -> str

# 插件管理
async def remove_plugin(plugin_name: str) -> bool
async def reload_plugin(plugin_name: str) -> bool
def load_plugin(plugin_name: str) -> Tuple[bool, int]

# 目录管理
def add_plugin_directory(plugin_directory: str) -> bool
def rescan_plugin_directory() -> Tuple[int, int]
```

### 插件生命周期管理
- **list_loaded_plugins()**: 列出当前加载的插件名称
- **list_registered_plugins()**: 列出所有已注册的插件名称
- **get_plugin_path()**: 获取指定插件的路径
- **remove_plugin()**: 卸载指定插件（异步操作）
- **reload_plugin()**: 重新加载指定插件（异步操作）
- **load_plugin()**: 加载指定插件，返回(成功状态, 处理数量)

### 目录管理机制
- **add_plugin_directory()**: 添加新的插件目录到扫描列表
- **rescan_plugin_directory()**: 重新扫描所有插件目录，返回(成功数量, 失败数量)

## 11. 组件管理API (component_manage_api)

### 核心函数签名
```python
# 插件信息查询
def get_all_plugin_info() -> Dict[str, PluginInfo]
def get_plugin_info(plugin_name: str) -> Optional[PluginInfo]

# 组件查询
def get_component_info(component_name: str, component_type: ComponentType) -> Optional[Union[CommandInfo, ActionInfo, EventHandlerInfo]]
def get_components_info_by_type(component_type: ComponentType) -> Dict[str, Union[CommandInfo, ActionInfo, EventHandlerInfo]]
def get_enabled_components_info_by_type(component_type: ComponentType) -> Dict[str, Union[CommandInfo, ActionInfo, EventHandlerInfo]]

# 特定组件查询
def get_registered_action_info(action_name: str) -> Optional[ActionInfo]
def get_registered_command_info(command_name: str) -> Optional[CommandInfo]
def get_registered_tool_info(tool_name: str) -> Optional[ToolInfo]
def get_registered_event_handler_info(event_handler_name: str) -> Optional[EventHandlerInfo]

# 组件管理
def globally_enable_component(component_name: str, component_type: ComponentType) -> bool
async def globally_disable_component(component_name: str, component_type: ComponentType) -> bool
def locally_enable_component(component_name: str, component_type: ComponentType, stream_id: str) -> bool
def locally_disable_component(component_name: str, component_type: ComponentType, stream_id: str) -> bool
def get_locally_disabled_components(stream_id: str, component_type: ComponentType) -> list[str]
```

### 组件管理层次
- **全局管理**: 影响所有聊天流的组件启用状态
- **局部管理**: 仅影响特定聊天流的组件启用状态
- **查询机制**: 支持按类型、名称、状态等多维度查询

### ComponentType枚举
```python
class ComponentType(Enum):
    ACTION = "action"
    COMMAND = "command"
    TOOL = "tool"
    EVENT_HANDLER = "event_handler"
```

## 12. 日志API (logging_api)

### 核心函数签名
```python
def get_logger(name: str) -> structlog.stdlib.BoundLogger
```

### 日志系统特性
- **结构化日志**: 使用structlog库提供结构化日志记录
- **日志级别**: 支持debug、info、warning、error、critical五个级别
- **命名空间**: 通过name参数区分不同模块的日志

### 使用示例
```python
logger = get_logger("my_plugin")

# 使用不同级别的日志记录
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

## 13. 工具API (tool_api)

### 核心函数签名
```python
def get_tool_instance(tool_name: str) -> Optional[BaseTool]
def get_llm_available_tool_definitions() -> List[Tuple[str, Dict[str, Any]]]
```

### 工具管理机制
- **get_tool_instance()**: 获取指定名称的工具实例，用于直接调用工具
- **get_llm_available_tool_definitions()**: 获取所有LLM可用的工具定义，用于LLM工具调用

### 工具定义格式
```python
# 工具定义字典格式
tool_definition = {
    "name": "tool_name",
    "description": "工具功能描述",
    "parameters": {
        "type": "object",
        "properties": {
            "param1": {
                "type": "string",
                "description": "参数描述"
            }
        },
        "required": ["param1"]
    }
}
```

### 使用示例
```python
# 获取工具实例
tool = tool_api.get_tool_instance("my_tool")
if tool:
    result = await tool.execute({"query": "test"})

# 获取LLM可用的工具定义
tools = tool_api.get_llm_available_tool_definitions()
for tool_name, tool_definition in tools:
    print(f"工具: {tool_name}")
    print(f"定义: {tool_definition}")
```

## API系统完整性总结

### 13大类API完整覆盖
1. **消息处理类**: send_api, message_api, chat_api
2. **AI生成类**: llm_api, generator_api
3. **数据管理类**: database_api, config_api, person_api
4. **系统管理类**: plugin_manage_api, component_manage_api, logging_api
5. **功能扩展类**: emoji_api, tool_api

### 技术架构特点
- **统一导入机制**: 所有API通过src.plugin_system.apis统一导入
- **异步编程支持**: 关键操作支持异步调用
- **类型安全**: 完整的类型注解和返回值定义
- **错误处理**: 统一的错误处理和异常机制
- **权限控制**: 支持全局和局部的权限管理

### 功能完整性
- **CRUD操作**: 完整的创建、读取、更新、删除操作支持
- **生命周期管理**: 插件和组件的完整生命周期管理
- **配置管理**: 全局和插件级别的配置访问
- **日志记录**: 结构化的日志记录系统
- **工具集成**: LLM工具调用和管理机制

这套完整的API系统提供了插件开发所需的全部功能支持，通过统一的接口设计、异步编程模式和完善的错误处理机制，实现了高性能、高可用性和易用性的平衡。
