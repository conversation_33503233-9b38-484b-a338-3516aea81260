[project]
name = "MaiBot"
version = "0.8.1"
description = "MaiCore 是一个基于大语言模型的可交互智能体"
requires-python = ">=3.10"
dependencies = [
    "aiohttp>=3.12.14",
    "apscheduler>=3.11.0",
    "colorama>=0.4.6",
    "cryptography>=45.0.5",
    "customtkinter>=5.2.2",
    "dotenv>=0.9.9",
    "faiss-cpu>=1.11.0",
    "fastapi>=0.116.0",
    "jieba>=0.42.1",
    "json-repair>=0.47.6",
    "jsonlines>=4.0.0",
    "maim-message>=0.3.8",
    "matplotlib>=3.10.3",
    "networkx>=3.4.2",
    "numpy>=2.2.6",
    "openai>=1.95.0",
    "packaging>=25.0",
    "pandas>=2.3.1",
    "peewee>=3.18.2",
    "pillow>=11.3.0",
    "psutil>=7.0.0",
    "pyarrow>=20.0.0",
    "pydantic>=2.11.7",
    "pymongo>=4.13.2",
    "pypinyin>=0.54.0",
    "python-dateutil>=2.9.0.post0",
    "python-dotenv>=1.1.1",
    "python-igraph>=0.11.9",
    "quick-algo>=0.1.3",
    "reportportal-client>=5.6.5",
    "requests>=2.32.4",
    "rich>=14.0.0",
    "ruff>=0.12.2",
    "scikit-learn>=1.7.0",
    "scipy>=1.15.3",
    "seaborn>=0.13.2",
    "setuptools>=80.9.0",
    "strawberry-graphql[fastapi]>=0.275.5",
    "structlog>=25.4.0",
    "toml>=0.10.2",
    "tomli>=2.2.1",
    "tomli-w>=1.2.0",
    "tomlkit>=0.13.3",
    "tqdm>=4.67.1",
    "urllib3>=2.5.0",
    "uvicorn>=0.35.0",
    "websockets>=15.0.1",
]


[tool.ruff]

include = ["*.py"]

# 行长度设置
line-length = 120

[tool.ruff.lint]
fixable = ["ALL"]
unfixable = []

# 如果一个变量的名称以下划线开头，即使它未被使用，也不应该被视为错误或警告。
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# 启用的规则
select = [
    "E", # pycodestyle 错误
    "F", # pyflakes
    "B", # flake8-bugbear
]

ignore = ["E711","E501"]

[tool.ruff.format]
docstring-code-format = true
indent-style = "space"


# 使用双引号表示字符串
quote-style = "double"

# 尊重魔法尾随逗号
# 例如：
# items = [
#     "apple",
#     "banana",
#     "cherry",
# ]
skip-magic-trailing-comma = false

# 自动检测合适的换行符
line-ending = "auto"
